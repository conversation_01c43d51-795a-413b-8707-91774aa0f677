# Interactive ESL Lesson Design: Daily Routines & Basketball

## Content Analysis
Based on the provided materials:
- **Theme**: Daily routines and basketball (O<PERSON> story)
- **Target**: 10-year-old students, A2-B1 level
- **Duration**: 90 minutes
- **Technology**: HTML/CSS/JavaScript/SVG only

## Key Vocabulary from Materials
### Daily Routines:
- have lunch, play video games, leave for school/work
- do homework, have breakfast, take a bath
- classes start, get up
- Time expressions: What time...?, When...?, Do you...?

### Basketball/Sports:
- basketball player, practice, team, games
- wake up, jogging, work hard, motivated
- professional, grades, relationship

## Lesson Structure Design

### PART 1 (30 minutes): Engage – Study – Apply

#### 1. Warm-up Logic Puzzles (10 minutes)
- **Theme**: Daily routine sequences and basketball training
- **Puzzles**: 
  - Clock time matching (SVG clocks)
  - Daily routine order puzzles
  - Basketball equipment matching
  - O<PERSON>'s schedule puzzles
- **Visual**: SVG graphics for clocks, sports equipment, daily activities

#### 2. Vocabulary Memory Game (10 minutes)
- **Grid**: 4x4 (A1-D4)
- **Pairs**: Correctly spelled vs scrambled daily routine words
- **Words**: wake up/ekaw pu, breakfast/katsaerfb, homework/krowemoh, practice/ecitcarp, etc.
- **Visual**: Card flip animations, team colors

#### 3. Interactive Vocabulary Quiz (10 minutes)
- **Format**: Multiple choice A-D
- **Questions**: Action-based descriptions
- **Example**: "What do you do when you're hungry in the morning?" → breakfast
- **Visual**: SVG icons for each activity

### PART 2 (30 minutes): Study & Apply

#### 4. Speed Reading Activity (15 minutes)
- **Text**: OJ Mayo story from the materials
- **Features**: 
  - Speed control 90-350 wpm
  - Yellow highlight following text
  - Comprehension questions
  - Gap fill exercises
  - Text reordering activity

#### 5. Pronunciation Minefield (8 minutes)
- **Grid**: 6x6 (A1-F6)
- **Teams**: 4 teams, 3 lives each
- **Words**: Daily routine vocabulary + basketball terms
- **Bombs**: 10 hidden bombs
- **Visual**: Team colors, explosion animations

#### 6. Tic-Tac-Toe Grammar (7 minutes)
- **Grid**: 7x7 (A1-G7)
- **Teams**: Red vs Blue
- **Symbols**: + (affirmative), - (negative), ? (question)
- **Goal**: Form correct sentences to claim squares

### PART 3 (30 minutes): Fun Review Competitions

#### 7. Jeopardy Game (15 minutes)
- **Categories**: 
  - Daily Routines (100-500 pts)
  - Basketball Facts (100-500 pts)
  - Time Expressions (100-500 pts)
  - Grammar Challenge (100-500 pts)
- **Features**: 30-second timer, team scoring

#### 8. Family Feud (15 minutes)
- **Categories**: 
  - "Things you do in the morning" (5 answers)
  - "Basketball equipment" (5 answers)
  - "School activities" (5 answers)
- **Points**: 5, 15, 20, 25, 35

## Technical Features

### Scoreboard System
- 4 teams: Red, Blue, Green, Yellow
- Editable team names
- Manual point adjustment
- Always visible or toggle

### Teacher Controls
- Next/Back navigation
- Shuffle/Reset options
- Answer reveal buttons
- Timer controls

### Visual Design
- Bright, playful color scheme
- Large, clear buttons
- Basketball and daily routine themed graphics
- SVG animations for smooth performance
- Responsive design for classroom display

### Interactive Elements
- Click-based navigation (no keyboard input)
- Smooth transitions and animations
- Visual feedback for all interactions
- Team color coding throughout
- Progress indicators

## Content Adaptation
- Age-appropriate language (avoid formal terms)
- Humor and engaging scenarios
- Clear, simple instructions
- Visual aids for comprehension
- Minimal text per screen

