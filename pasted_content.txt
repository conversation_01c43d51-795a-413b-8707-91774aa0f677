📌 Ultimate prompt for the lesson plan
(Copy/paste and use in ChatGPT / Cursor / <PERSON> / <PERSON>pilot etc.)

Build a complete, interactive 90-minute ESL lesson web app for 10-year-old students at A2–B1 level.
No user inputs needed except teacher control (clicks/spacebar).
Fun, colorful, dynamic, visually engaging, with minimal text & age-appropriate design.
HTML + CSS + JavaScript (with SVG where suitable).
Each game auto-runs or progresses by teacher click.
The whole lesson must have scoreboard functionality to track team scores across games (4 teams).

The lesson must include:

🌟 PART 1 (First 30 minutes: Engage – Study – Apply)
🧩 1. Warm-up Logic Puzzles (10-15 puzzles)
Puzzles related to today's topic: e.g., shapes, scrambled detective notes, secret grocery lists, trick questions.

Use SVG for puzzle visuals.

Age-appropriate, friendly, funny language.

Show Answer button.

Auto-shuffle order each time.

🧠 2. Vocabulary Memory Game
4x4 grid: A1–D4.

Pairs: one correctly spelled word, one scrambled version.

Random placement & shuffle button.

Words come from today’s content (if not enough, add thematically similar words).

Each card shows label (A1, B3 etc.) to call out.

Cards flip on click; matching pair stays open, wrong pair flips back.

✏️ 3. Interactive Vocabulary Description Quiz
Multiple choice (A–D).

Questions describe words using simple action words.

Avoid formal language (no “according,” “in order to,” etc.).

Visual aids (images/SVG).

Age-appropriate humor.

Random question order each time.

📖 PART 2 (Second 30 minutes: Study & Apply)
⏩ 4. Speed Reading Activity
Text from lesson content (e.g., Unit 14 comic story, superheroes, special buildings).

Manual speed control: 90–350 wpm.

Yellow highlight smoothly follows text without moving the text itself.

Whole text visible inside a non-stretching container.

After reading:

6 comprehension MCQs (multiple choice).

10 gap fills (missing key words from new vocab/action words).

Text reordering: shuffle text chunks, kids reorder by clicking “swap” buttons.

💣 5. Pronunciation Minefield Game
Grid 6x6 (A1–F6).

4 teams, 3 lives each.

10 bombs hidden behind random words; others are safe.

Content-focused words (or similar-sounding words if not enough).

Teams take turns choosing grid squares; wrong guess = lose life.

Team colors: fun & bright.

Last team standing wins.

❌➕❓ 6. Tic-Tac-Toe Grammar Game
Grid 7x7 (A1–G7).

Teams play Red vs Blue.

Each square: a vocab word + symbol (+ affirmative, - negative, ? question).

Teams form correct sentence in that form to claim square.

Goal: get 4 in a row (horizontal, vertical, diagonal).

Squares turn team color when claimed.

Command system: e.g., “Team Red chooses B5 negative.”

🏆 PART 3 (Final 30 minutes: Fun review competitions)
🎲 Jeopardy Game
4 teams.

Topics: e.g., “Vocabulary,” “Grammar,” “Funny Facts,” “Superheroes,” etc.

Board with clickable points: 100–500.

Questions pop up in modal:

Multiple choice (A–D).

30-second timer.

“Back” button (in case of tech issue).

“Next team” button.

If team answers wrong → don’t show answer; keep question live for others.

If correct → disable that point box.

Harder questions (more misleading answers) worth more points.

👨‍👩‍👧 Family Feud Game
Categories based on lesson vocab/themes.

Each category has 5 hidden answers (e.g., 5 different “things superheroes wear”).

Teacher clicks to reveal each answer.

Points: 5, 15, 20, 25, 35 (least obvious answer = highest points).

Teams guess; reveal after guess.

Track points.

📌 BONUS FEATURES & LOGIC
✅ Scoreboard

Always visible or toggled.

Team names editable (Red, Blue, Green, Yellow).

Add points manually if needed (e.g., bonus for best teamwork).

✅ Teacher panel / buttons

“Next activity,” “Back,” “Shuffle,” “Reveal answer,” “Reset scores,” etc.

✅ Design

Colorful, playful fonts.

Large buttons & clear labels (A1, B3 etc.).

Visual consistency.

Minimal reading per slide/screen.

✅ Instructions per game

Short, age-appropriate: “Choose a square!”, “Make a question!”, “Guess now!”

⚙️ Tech summary for dev prompt
HTML + CSS (fun theme).

Vanilla JavaScript .

Use SVG for puzzle shapes & highlight.

Responsive design: 1 big display screen.

All actions controlled by clicks (no keyboard inputs).

🧩 Topics & content
A2–B1 vocab: superheroes, city buildings, action verbs, feelings, school objects.

Age-appropriate, funny, but challenging.

✅ Final note:
Include clear game logic, turn systems, timers, shuffle/randomizers, team color coding, visual feedback, and teacher control for pace.
No external API, no external libraries unless very light (e.g., anime.js for animation).
Make sure grammar and spelling in prompts, answers, and examples is perfect, native & age-appropriate.

