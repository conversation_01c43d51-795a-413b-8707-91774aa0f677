<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Routines & Basketball - Interactive ESL Lesson</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Scoreboard -->
    <div id="scoreboard" class="scoreboard">
        <h2>Team Scores</h2>
        <div class="teams">
            <div class="team red">
                <span class="team-name" contenteditable="true">Red Team</span>
                <span class="score">0</span>
                <button onclick="adjustScore('red', 1)">+</button>
                <button onclick="adjustScore('red', -1)">-</button>
            </div>
            <div class="team blue">
                <span class="team-name" contenteditable="true">Blue Team</span>
                <span class="score">0</span>
                <button onclick="adjustScore('blue', 1)">+</button>
                <button onclick="adjustScore('blue', -1)">-</button>
            </div>
            <div class="team green">
                <span class="team-name" contenteditable="true">Green Team</span>
                <span class="score">0</span>
                <button onclick="adjustScore('green', 1)">+</button>
                <button onclick="adjustScore('green', -1)">-</button>
            </div>
            <div class="team yellow">
                <span class="team-name" contenteditable="true">Yellow Team</span>
                <span class="score">0</span>
                <button onclick="adjustScore('yellow', 1)">+</button>
                <button onclick="adjustScore('yellow', -1)">-</button>
            </div>
        </div>
        <button onclick="resetScores()">Reset All</button>
    </div>

    <!-- Main Content -->
    <div id="main-content">
        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active">
            <h1>Daily Routines & Basketball</h1>
            <h2>Interactive ESL Lesson</h2>
            <div class="basketball-animation">
                <svg width="200" height="200" viewBox="0 0 200 200">
                    <circle cx="100" cy="100" r="80" fill="#ff6b35" stroke="#000" stroke-width="3"/>
                    <path d="M 40 100 Q 100 60 160 100" stroke="#000" stroke-width="3" fill="none"/>
                    <path d="M 40 100 Q 100 140 160 100" stroke="#000" stroke-width="3" fill="none"/>
                    <path d="M 100 20 L 100 180" stroke="#000" stroke-width="3"/>
                </svg>
            </div>
            <p>A fun lesson about daily routines featuring OJ Mayo!</p>
            <button onclick="startLesson()" class="start-btn">Start Lesson</button>
        </div>

        <!-- Activity Menu -->
        <div id="activity-menu" class="screen">
            <h2>Choose an Activity</h2>
            <div class="activity-grid">
                <button onclick="showActivity('puzzles')" class="activity-btn">
                    <span>🧩</span>
                    <h3>Logic Puzzles</h3>
                    <p>Daily routine puzzles</p>
                </button>
                <button onclick="showActivity('memory')" class="activity-btn">
                    <span>🧠</span>
                    <h3>Memory Game</h3>
                    <p>Match the words</p>
                </button>
                <button onclick="showActivity('vocab-quiz')" class="activity-btn">
                    <span>✏️</span>
                    <h3>Vocabulary Quiz</h3>
                    <p>Multiple choice fun</p>
                </button>
                <button onclick="showActivity('speed-reading')" class="activity-btn">
                    <span>📖</span>
                    <h3>Speed Reading</h3>
                    <p>OJ Mayo's story</p>
                </button>
                <button onclick="showActivity('minefield')" class="activity-btn">
                    <span>💣</span>
                    <h3>Pronunciation Minefield</h3>
                    <p>Avoid the bombs!</p>
                </button>
                <button onclick="showActivity('tic-tac-toe')" class="activity-btn">
                    <span>❌</span>
                    <h3>Grammar Tic-Tac-Toe</h3>
                    <p>Make sentences</p>
                </button>
                <button onclick="showActivity('jeopardy')" class="activity-btn">
                    <span>🏆</span>
                    <h3>Jeopardy</h3>
                    <p>Answer to win points</p>
                </button>
                <button onclick="showActivity('family-feud')" class="activity-btn">
                    <span>👨‍👩‍👧</span>
                    <h3>Family Feud</h3>
                    <p>Survey says...</p>
                </button>
            </div>
        </div>

        <!-- Logic Puzzles Activity -->
        <div id="puzzles" class="screen activity">
            <h2>Logic Puzzles</h2>
            <div id="puzzle-container">
                <div id="current-puzzle">
                    <!-- Puzzle content will be inserted here -->
                </div>
                <div class="puzzle-controls">
                    <button onclick="showAnswer()">Show Answer</button>
                    <button onclick="nextPuzzle()">Next Puzzle</button>
                    <button onclick="shufflePuzzles()">Shuffle</button>
                </div>
            </div>
            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>

        <!-- Memory Game Activity -->
        <div id="memory" class="screen activity">
            <h2>Vocabulary Memory Game</h2>
            <div id="memory-grid" class="memory-grid">
                <!-- Memory cards will be generated here -->
            </div>
            <div class="memory-controls">
                <button onclick="shuffleMemoryCards()">Shuffle</button>
                <button onclick="resetMemoryGame()">Reset Game</button>
            </div>
            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>

        <!-- Vocabulary Quiz Activity -->
        <div id="vocab-quiz" class="screen activity">
            <h2>Vocabulary Quiz</h2>
            <div id="quiz-container">
                <div id="question-display">
                    <!-- Question will be displayed here -->
                </div>
                <div id="quiz-options">
                    <!-- Options will be displayed here -->
                </div>
                <div class="quiz-controls">
                    <button onclick="nextQuestion()">Next Question</button>
                    <button onclick="shuffleQuestions()">Shuffle</button>
                </div>
            </div>
            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>

        <!-- Speed Reading Activity -->
        <div id="speed-reading" class="screen activity">
            <h2>Speed Reading: OJ Mayo's Story</h2>
                <div class="speed-reading-controls">
                    <label for="speed-slider">Speed (WPM): <span id="speed-display">200</span></label>
                    <input type="range" id="speed-slider" min="90" max="350" value="200" onchange="updateSpeed()">
                    <label>Highlight:</label>
                    <select id="highlight-granularity" onchange="updateHighlightGranularity()">
                        <option value="1">1 Word</option>
                        <option value="2">2 Words</option>
                        <option value="3">3 Words</option>
                        <option value="sentence">Sentence</option>
                    </select>
                    <button onclick="startReading()">Start Reading</button>
                    <button onclick="pauseReading()">Pause</button>
                    <button onclick="resetReading()">Reset</button>
                </div>
                <div id="reading-text" class="reading-text">
                    <!-- OJ Mayo's story will be displayed here -->
                </div>
                <div id="comprehension-section">
                    <h3>Comprehension Questions</h3>
                    <div id="comprehension-questions">
                        <!-- Questions will be generated here -->
                    </div>
                    <button onclick="showAllComprehensionAnswers()">Show All Answers</button>
                </div>
                <div id="gap-fill-section">
                    <h3>Gap Fill Activity</h3>
                    <div id="gap-fill-text">
                        <!-- Gap fill text will be generated here -->
                    </div>
                    <button onclick="showAllGapFillAnswers()">Show All Answers</button>
                </div>            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>

        <!-- Pronunciation Minefield Activity -->
        <div id="minefield" class="screen activity">
            <h2>Pronunciation Minefield</h2>
            <div class="minefield-info">
                <div class="team-lives">
                    <div class="team-life red">Red: <span id="red-lives">3</span> ❤️</div>
                    <div class="team-life blue">Blue: <span id="blue-lives">3</span> ❤️</div>
                    <div class="team-life green">Green: <span id="green-lives">3</span> ❤️</div>
                    <div class="team-life yellow">Yellow: <span id="yellow-lives">3</span> ❤️</div>
                </div>
                <div class="current-team">Current Team: <span id="current-minefield-team">Red</span></div>
            </div>
            <div id="minefield-grid" class="minefield-grid">
                <!-- 6x6 grid will be generated here -->
            </div>
            <div class="minefield-controls">
                <button onclick="resetMinefield()">Reset Game</button>
            </div>
            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>

        <!-- Tic-Tac-Toe Grammar Activity -->
        <div id="tic-tac-toe" class="screen activity">
            <h2>Grammar Tic-Tac-Toe</h2>
            <div class="ttt-info">
                <div class="current-team">Current Team: <span id="current-ttt-team">Red</span></div>
                <div class="ttt-instructions">Make a sentence using the word and symbol!</div>
            </div>
            <div id="ttt-grid" class="ttt-grid">
                <!-- 7x7 grid will be generated here -->
            </div>
            <div class="ttt-controls">
                <button onclick="resetTicTacToe()">Reset Game</button>
            </div>
            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>

        <!-- Jeopardy Activity -->
        <div id="jeopardy" class="screen activity">
            <h2>Jeopardy Game</h2>
            <div class="jeopardy-info">
                <div class="current-team">Current Team: <span id="current-jeopardy-team">Red</span></div>
                <div id="timer-display" class="timer hidden">Time: <span id="timer-seconds">30</span>s</div>
            </div>
            <div id="jeopardy-board" class="jeopardy-board">
                <!-- Jeopardy board will be generated here -->
            </div>
            <div id="question-modal" class="modal hidden">
                <div class="modal-content">
                    <div id="modal-question"></div>
                    <div id="modal-options"></div>
                    <div class="modal-controls">
                        <button onclick="closeModal()">Back</button>
                        <button onclick="nextJeopardyTeam()">Next Team</button>
                    </div>
                </div>
            </div>
            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>

        <!-- Family Feud Activity -->
        <div id="family-feud" class="screen activity">
            <h2>Family Feud</h2>
            <div class="feud-info">
                <div class="current-team">Current Team: <span id="current-feud-team">Red</span></div>
                <div class="category-display">Category: <span id="current-category">Things you do in the morning</span></div>
            </div>
            <div id="feud-board" class="feud-board">
                <!-- Family Feud answers will be displayed here -->
            </div>
            <div class="feud-controls">
                <button onclick="nextFeudCategory()">Next Category</button>
                <button onclick="nextFeudTeam()">Next Team</button>
                <button onclick="resetFeud()">Reset</button>
            </div>
            <button onclick="backToMenu()" class="back-btn">Back to Menu</button>
        </div>
    </div>

    <!-- Teacher Controls -->
    <div id="teacher-controls" class="teacher-controls">
        <button onclick="toggleScoreboard()">Toggle Scoreboard</button>
        <button onclick="backToMenu()">Main Menu</button>
    </div>

    <script src="script.js"></script>
</body>
</html>

